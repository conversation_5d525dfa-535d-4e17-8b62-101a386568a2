import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import numpy as np
import soundfile as sf
import threading
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

try:
    import librosa
    LIBROSA_OK = True
except ImportError:
    LIBROSA_OK = False

class TrackDetectorApp:
    """
    Vylepšená desktopová aplikace pro detekci a vizualizaci skladeb v audio souborech.
    - Využívá rychlou vektorizovanou analýzu pomocí NumPy.
    - Zobrazuje waveformu s vyznačenými hranicemi detekovaných skladeb.
    - Poskytuje přehledné a responzivní uživatelské rozhraní.
    """
    def __init__(self, root):
        self.root = root
        self.root.title("Vinyl Track Analyzer PRO v2.0")
        self.root.geometry("850x800") # Větší výchozí okno pro lepší zobrazení
        self.root.minsize(700, 600)
        
        # Stylován<PERSON>
        self.style = ttk.Style()
        self.style.theme_use('clam') # Modernějš<PERSON> vzhled
        self.style.configure("TButton", padding=6, font=('Segoe UI', 10))
        self.style.configure("Header.TLabel", font=('Segoe UI', 14, 'bold'))
        self.style.configure("Status.TLabel", font=('Segoe UI', 9))
        self.style.configure("TLabelFrame.Label", font=('Segoe UI', 11, 'bold'))
        
        # --- Hlavní rám ---
        self.main_pane = ttk.PanedWindow(root, orient=tk.VERTICAL)
        self.main_pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Horní panel (ovládací prvky) ---
        top_frame = ttk.Frame(self.main_pane, padding=10)
        self.main_pane.add(top_frame, weight=0)

        self.header = ttk.Label(top_frame, text="Detekce a vizualizace skladeb", style="Header.TLabel")
        self.header.pack(pady=(0, 15), anchor='w')
        
        # Výběr souboru
        file_frame = ttk.Frame(top_frame)
        file_frame.pack(fill=tk.X, pady=5)
        self.btn_open = ttk.Button(file_frame, text="Vybrat WAV soubor", command=self.open_file, width=20)
        self.btn_open.pack(side=tk.LEFT, padx=(0, 10))
        self.file_label = ttk.Label(file_frame, text="Žádný soubor vybrán", style="Status.TLabel", anchor='w')
        self.file_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Informace o souboru
        self.duration_label = ttk.Label(top_frame, text="", font=('Segoe UI', 10, 'italic'))
        self.duration_label.pack(fill=tk.X, pady=(5, 10), anchor='w')

        # Tlačítko analýzy a progress bar
        analysis_frame = ttk.Frame(top_frame)
        analysis_frame.pack(fill=tk.X, pady=5)
        self.btn_analyze = ttk.Button(analysis_frame, text="Spustit analýzu", command=self.start_analysis, state=tk.DISABLED)
        self.btn_analyze.pack(side=tk.LEFT, padx=(0, 10))
        self.progress = ttk.Progressbar(analysis_frame, mode='determinate')
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.progress_label = ttk.Label(analysis_frame, text="0%", width=5, anchor='e')
        self.progress_label.pack(side=tk.RIGHT)

        # --- Výběr metody detekce ---
        method_frame = ttk.Frame(top_frame)
        method_frame.pack(fill=tk.X, pady=(5, 0))
        self.use_onsets = tk.BooleanVar(value=False)
        self.chk_onsets = ttk.Checkbutton(
            method_frame,
            text="Použít pomalou (onset) metodu pro kontinuální nahrávky",
            variable=self.use_onsets)
        self.chk_onsets.pack(anchor='w')
        if not LIBROSA_OK:
            self.chk_onsets.configure(state=tk.DISABLED)

        # --- Slider pro prah ticha ---
        self.silence_threshold_var = tk.DoubleVar(value=0.02)
        sil_frame = ttk.Frame(top_frame)
        sil_frame.pack(fill=tk.X, pady=5)
        ttk.Label(sil_frame, text="Prah ticha:").pack(side=tk.LEFT)
        ttk.Scale(sil_frame, from_=0.005, to=0.1, orient=tk.HORIZONTAL,
                  variable=self.silence_threshold_var).pack(fill=tk.X, expand=True)
        
        # --- Spodní panel (výsledky a graf) ---
        bottom_pane = ttk.PanedWindow(self.main_pane, orient=tk.HORIZONTAL)
        self.main_pane.add(bottom_pane, weight=1)

        # Panel s výsledky (seznam skladeb)
        results_list_frame = ttk.LabelFrame(bottom_pane, text="Detekované skladby", padding=10)
        bottom_pane.add(results_list_frame, weight=1)
        
        # Záhlaví seznamu
        header_frame = ttk.Frame(results_list_frame)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(header_frame, text="#", width=4, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        ttk.Label(header_frame, text="Délka", width=15, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        ttk.Label(header_frame, text="(sekundy)", font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        
        # Scrollovatelný seznam
        self.results_canvas = tk.Canvas(results_list_frame, borderwidth=0, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(results_list_frame, orient="vertical", command=self.results_canvas.yview)
        self.results_content_frame = ttk.Frame(self.results_canvas)
        self.results_content_frame.bind("<Configure>", lambda e: self.results_canvas.configure(scrollregion=self.results_canvas.bbox("all")))
        self.results_canvas.create_window((0, 0), window=self.results_content_frame, anchor="nw")
        self.results_canvas.configure(yscrollcommand=self.scrollbar.set)
        self.results_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Panel s grafem
        self.plot_frame = ttk.LabelFrame(bottom_pane, text="Waveforma a hranice skladeb", padding=10)
        bottom_pane.add(self.plot_frame, weight=3)
        
        self.fig, self.ax = plt.subplots(facecolor='#f0f0f0')
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.plot_frame)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(fill=tk.BOTH, expand=True)
        self.init_plot()
        
        # Status bar
        self.status = ttk.Label(root, text="Připraveno", style="Status.TLabel", anchor='w', relief=tk.SUNKEN, padding=5)
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
        
        # --- Proměnné aplikace ---
        self.file_path = ""
        self.audio_data = None
        self.samplerate = None
        self.analysis_thread = None

    def open_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("WAV soubory", "*.wav")])
        if not file_path: return
            
        self.file_path = file_path
        self.file_label.config(text=os.path.basename(file_path))
        self.btn_analyze.config(state=tk.NORMAL)
        self.clear_results()
        self.status.config(text="Načítám informace o souboru...")
        
        try:
            info = sf.info(self.file_path)
            self.samplerate = info.samplerate
            duration_sec = info.duration
            self.duration_label.config(text=f"Celková délka: {self.format_duration(duration_sec)}  |  Vzorkovací frekvence: {self.samplerate} Hz")
            self.status.config(text="Soubor připraven k analýze")
        except Exception as e:
            self.show_error(f"Nelze přečíst informace o souboru: {e}")
            self.file_path = ""

    def start_analysis(self):
        if not self.file_path: return
        self.set_ui_state(tk.DISABLED)
        self.update_progress(0)
        self.status.config(text="Spouštím analýzu...")
        self.clear_results()
        self.analysis_thread = threading.Thread(target=self.analyze_audio_vectorized, daemon=True)
        self.analysis_thread.start()

    def analyze_audio_vectorized(self):
        try:
            self.update_status("Načítám audio data do paměti...")
            start_time = time.time()

            if self.use_onsets.get():
                if not LIBROSA_OK:
                    raise RuntimeError("Modul librosa není nainstalovaný.")

                self.update_status("Načítám audio přes librosa...")
                self.update_progress(10)
                y, sr = librosa.load(self.file_path, sr=None, mono=True)
                self.samplerate = sr
                # Pro vizualizaci potřebujeme stereo formát
                self.audio_data = y.reshape(-1, 1) if y.ndim == 1 else y

                self.update_status("Hledám onsety...")
                self.update_progress(50)
                onset_frames = librosa.onset.onset_detect(
                    y=y, sr=sr, hop_length=512, backtrack=True)
                onset_times = librosa.frames_to_time(
                    onset_frames, sr=sr, hop_length=512)
                self.update_progress(80)

                # Přidáme konec souboru jako poslední hranici
                track_boundaries_samples = []
                duration_samples = len(y)
                min_track_len = 5.0  # sekundy
                for i, t in enumerate(onset_times):
                    start = librosa.time_to_samples(t, sr=sr)
                    end = librosa.time_to_samples(
                        onset_times[i+1], sr=sr) if i+1 < len(onset_times) else duration_samples
                    if (end - start) / sr > min_track_len:
                        track_boundaries_samples.append((start, end))

            else:
                # původní rychlá metoda ticha
                data, samplerate = sf.read(self.file_path, always_2d=True)
                self.audio_data = data
                self.samplerate = samplerate

                mono_data = np.mean(data, axis=1)
                max_val = np.max(np.abs(mono_data))
                if max_val > 0:
                    mono_data = mono_data / max_val

                self.update_status("Hledám tichá místa (vektorizovaná metoda)...")
                self.update_progress(25)

                silence_threshold = self.silence_threshold_var.get()
                min_silence_duration = 1.0
                min_silence_samples = int(samplerate * min_silence_duration)

                is_silent = np.abs(mono_data) < silence_threshold
                self.update_progress(50)

                state_changes = np.diff(is_silent.astype(np.int8))
                silence_starts = np.where(state_changes == 1)[0] + 1
                silence_ends = np.where(state_changes == -1)[0] + 1
                self.update_progress(75)

                if len(silence_starts) == 0 or len(silence_ends) == 0:
                    track_boundaries_samples = [(0, len(mono_data))]
                else:
                    if silence_ends[0] < silence_starts[0]:
                        silence_starts = np.insert(silence_starts, 0, 0)
                    if silence_starts[-1] > silence_ends[-1]:
                        silence_ends = np.append(silence_ends, len(mono_data))

                    boundaries = [0]
                    for start, end in zip(silence_starts, silence_ends):
                        if (end - start) >= min_silence_samples:
                            boundaries.append(start)
                            boundaries.append(end)
                    boundaries.append(len(mono_data))
                    boundaries = sorted(list(set(boundaries)))

                    track_boundaries_samples = []
                    min_track_len = 5.0  # sekundy
                    for i in range(len(boundaries) - 1):
                        start_sample, end_sample = boundaries[i], boundaries[i+1]
                        if (end_sample - start_sample) / samplerate > min_track_len and not np.all(is_silent[start_sample:end_sample]):
                            track_boundaries_samples.append((start_sample, end_sample))

            total_time = time.time() - start_time
            self.update_status(f"Analýza dokončena: {len(track_boundaries_samples)} skladeb nalezeno za {total_time:.2f}s")
            self.root.after(0, self.show_results, track_boundaries_samples)

        except Exception as e:
            self.root.after(0, self.show_error, f"Během analýzy došlo k chybě: {e}")
        finally:
            self.root.after(0, self.set_ui_state, tk.NORMAL)

    def show_results(self, track_boundaries_samples):
        self.clear_results_list()
        
        if not track_boundaries_samples:
            ttk.Label(self.results_content_frame, text="Nebyla detekována žádná skladba.").pack(pady=10)
        else:
            for i, (start, end) in enumerate(track_boundaries_samples):
                duration = (end - start) / self.samplerate
                frame = ttk.Frame(self.results_content_frame)
                frame.pack(fill=tk.X, pady=2, padx=5)
                ttk.Label(frame, text=f"{i+1}.", width=4).pack(side=tk.LEFT)
                ttk.Label(frame, text=self.format_duration(duration), width=15, anchor=tk.W).pack(side=tk.LEFT)
                ttk.Label(frame, text=f"({duration:.3f} s)", anchor=tk.W).pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.update_progress(100)
        self.plot_waveform(track_boundaries_samples)

    def plot_waveform(self, boundaries_samples):
        self.ax.clear()
        
        mono_data = np.mean(self.audio_data, axis=1)
        
        # Downsampling pro rychlé vykreslení (max 50k bodů)
        max_points = 50000
        step = max(1, len(mono_data) // max_points)
        plot_data = mono_data[::step]
        time_axis = np.linspace(0, len(mono_data) / self.samplerate, len(plot_data))
        
        self.ax.plot(time_axis, plot_data, color='#007acc', linewidth=0.7)
        
        # Vykreslení hranic skladeb
        track_starts = [s / self.samplerate for s, e in boundaries_samples]
        for start_time in track_starts[1:]:
            self.ax.axvline(x=start_time, color='r', linestyle='--', linewidth=1.2, label='Hranice skladby')

        if len(track_starts) > 1:
            handles, labels = self.ax.get_legend_handles_labels()
            by_label = dict(zip(labels, handles))
            self.ax.legend(by_label.values(), by_label.keys(), loc='upper right')

        self.ax.set_title("Audio Waveforma", fontsize=10)
        self.ax.set_xlabel("Čas (s)", fontsize=9)
        self.ax.set_ylabel("Amplituda", fontsize=9)
        self.ax.grid(True, linestyle=':', alpha=0.6)
        self.ax.margins(x=0.01)
        self.fig.tight_layout()
        self.canvas.draw()

    def init_plot(self):
        self.ax.clear()
        self.ax.set_title("Načtěte soubor a spusťte analýzu", fontsize=10)
        self.ax.set_xlabel("Čas (s)", fontsize=9)
        self.ax.set_ylabel("Amplituda", fontsize=9)
        self.ax.grid(True, linestyle=':', alpha=0.6)
        self.fig.tight_layout()
        self.canvas.draw()

    def format_duration(self, seconds):
        minutes = int(seconds // 60)
        seconds_rem = seconds % 60
        return f"{minutes:02d}:{seconds_rem:06.3f}"

    def show_error(self, error_msg):
        messagebox.showerror("Chyba", error_msg)
        self.status.config(text="Chyba při analýze")

    def set_ui_state(self, state):
        self.btn_open.config(state=state)
        self.btn_analyze.config(state=state if self.file_path else tk.DISABLED)

    def clear_results_list(self):
        for widget in self.results_content_frame.winfo_children():
            widget.destroy()

    def clear_results(self):
        self.clear_results_list()
        self.init_plot()

    def update_progress(self, value):
        value = int(value)
        self.root.after(0, lambda: self.progress.config(value=value))
        self.root.after(0, lambda: self.progress_label.config(text=f"{value}%"))

    def update_status(self, message):
        self.root.after(0, lambda: self.status.config(text=message))

if __name__ == "__main__":
    root = tk.Tk()
    app = TrackDetectorApp(root)
    root.mainloop()