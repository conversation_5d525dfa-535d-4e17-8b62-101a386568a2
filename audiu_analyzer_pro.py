import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import numpy as np
import soundfile as sf
import threading
import time
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.ticker import FuncFormatter

try:
    import librosa
    LIBROSA_OK = True
except ImportError:
    LIBROSA_OK = False

class TrackDetectorApp:
    """
    Vylepšená desktopová aplikace pro detekci a vizualizaci skladeb v audio souborech.
    - Využívá rychlou vektorizovanou analýzu pomocí NumPy.
    - Zobrazuje waveformu s vyznačenými hranicemi detekovaných skladeb.
    - Poskytuje přehledné a responzivní uživatelské rozhraní.
    """
    def __init__(self, root):
        self.root = root
        self.root.title("Vinyl Track Analyzer PRO v2.0")
        self.root.geometry("850x800") # V<PERSON>t<PERSON><PERSON> výchozí okno pro lepší zobrazení
        self.root.minsize(700, 600)
        
        # Stylování
        self.style = ttk.Style()
        self.style.theme_use('clam') # Modernější vzhled
        self.style.configure("TButton", padding=6, font=('Segoe UI', 10))
        self.style.configure("Header.TLabel", font=('Segoe UI', 14, 'bold'))
        self.style.configure("Status.TLabel", font=('Segoe UI', 9))
        self.style.configure("TLabelFrame.Label", font=('Segoe UI', 11, 'bold'))
        
        # --- Hlavní rám ---
        self.main_pane = ttk.PanedWindow(root, orient=tk.VERTICAL)
        self.main_pane.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- Horní panel (ovládací prvky) ---
        top_frame = ttk.Frame(self.main_pane, padding=10)
        self.main_pane.add(top_frame, weight=0)

        self.header = ttk.Label(top_frame, text="Detekce a vizualizace skladeb", style="Header.TLabel")
        self.header.pack(pady=(0, 15), anchor='w')
        
        # Výběr souboru
        file_frame = ttk.Frame(top_frame)
        file_frame.pack(fill=tk.X, pady=5)
        self.btn_open = ttk.Button(file_frame, text="Vybrat WAV soubor", command=self.open_file, width=20)
        self.btn_open.pack(side=tk.LEFT, padx=(0, 10))
        self.file_label = ttk.Label(file_frame, text="Žádný soubor vybrán", style="Status.TLabel", anchor='w')
        self.file_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Informace o souboru
        self.duration_label = ttk.Label(top_frame, text="", font=('Segoe UI', 10, 'italic'))
        self.duration_label.pack(fill=tk.X, pady=(5, 10), anchor='w')

        # Tlačítko analýzy a progress bar
        analysis_frame = ttk.Frame(top_frame)
        analysis_frame.pack(fill=tk.X, pady=5)
        self.btn_analyze = ttk.Button(analysis_frame, text="Spustit analýzu", command=self.start_analysis, state=tk.DISABLED)
        self.btn_analyze.pack(side=tk.LEFT, padx=(0, 10))
        self.progress = ttk.Progressbar(analysis_frame, mode='determinate')
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.progress_label = ttk.Label(analysis_frame, text="0%", width=5, anchor='e')
        self.progress_label.pack(side=tk.RIGHT)

        # --- Výběr metody detekce ---
        method_frame = ttk.Frame(top_frame)
        method_frame.pack(fill=tk.X, pady=(5, 0))
        self.use_onsets = tk.BooleanVar(value=False)
        self.chk_onsets = ttk.Checkbutton(
            method_frame,
            text="Použít pomalou (onset) metodu pro kontinuální nahrávky",
            variable=self.use_onsets)
        self.chk_onsets.pack(anchor='w')
        if not LIBROSA_OK:
            self.chk_onsets.configure(state=tk.DISABLED)

        # --- Konfigurovatelné parametry ---
        params_frame = ttk.LabelFrame(top_frame, text="Parametry detekce", padding=10)
        params_frame.pack(fill=tk.X, pady=5)

        # Slider pro prah ticha
        self.silence_threshold_var = tk.DoubleVar(value=0.02)
        sil_frame = ttk.Frame(params_frame)
        sil_frame.pack(fill=tk.X, pady=2)
        ttk.Label(sil_frame, text="Prah ticha:").pack(side=tk.LEFT)
        self.silence_scale = ttk.Scale(sil_frame, from_=0.005, to=0.1, orient=tk.HORIZONTAL,
                  variable=self.silence_threshold_var)
        self.silence_scale.pack(fill=tk.X, expand=True)

        # Minimální délka ticha
        self.min_silence_var = tk.DoubleVar(value=1.0)
        silence_len_frame = ttk.Frame(params_frame)
        silence_len_frame.pack(fill=tk.X, pady=2)
        ttk.Label(silence_len_frame, text="Min. délka ticha (s):").pack(side=tk.LEFT)
        ttk.Scale(silence_len_frame, from_=0.1, to=5.0, orient=tk.HORIZONTAL,
                  variable=self.min_silence_var).pack(fill=tk.X, expand=True)

        # Minimální délka skladby
        self.min_track_var = tk.DoubleVar(value=5.0)
        track_len_frame = ttk.Frame(params_frame)
        track_len_frame.pack(fill=tk.X, pady=2)
        ttk.Label(track_len_frame, text="Min. délka skladby (s):").pack(side=tk.LEFT)
        ttk.Scale(track_len_frame, from_=1.0, to=30.0, orient=tk.HORIZONTAL,
                  variable=self.min_track_var).pack(fill=tk.X, expand=True)

        # Callback pro dynamické ovládání slideru
        self.use_onsets.trace_add('write', self._on_method_change)
        
        # --- Spodní panel (výsledky a graf) ---
        bottom_pane = ttk.PanedWindow(self.main_pane, orient=tk.HORIZONTAL)
        self.main_pane.add(bottom_pane, weight=1)

        # Panel s výsledky (seznam skladeb)
        results_list_frame = ttk.LabelFrame(bottom_pane, text="Detekované skladby", padding=10)
        bottom_pane.add(results_list_frame, weight=1)
        
        # Záhlaví seznamu
        header_frame = ttk.Frame(results_list_frame)
        header_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(header_frame, text="#", width=3, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        ttk.Label(header_frame, text="Začátek", width=12, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        ttk.Label(header_frame, text="Konec", width=12, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        ttk.Label(header_frame, text="Délka", width=12, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
        
        # Scrollovatelný seznam
        self.results_canvas = tk.Canvas(results_list_frame, borderwidth=0, highlightthickness=0)
        self.scrollbar = ttk.Scrollbar(results_list_frame, orient="vertical", command=self.results_canvas.yview)
        self.results_content_frame = ttk.Frame(self.results_canvas)
        self.results_content_frame.bind("<Configure>", lambda e: self.results_canvas.configure(scrollregion=self.results_canvas.bbox("all")))
        self.results_canvas.create_window((0, 0), window=self.results_content_frame, anchor="nw")
        self.results_canvas.configure(yscrollcommand=self.scrollbar.set)
        self.results_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Souhrn výsledků
        self.summary_frame = ttk.Frame(results_list_frame)
        self.summary_frame.pack(fill=tk.X, pady=(10, 0))
        self.summary_label = ttk.Label(self.summary_frame, text="", font=('Segoe UI', 9, 'italic'))
        self.summary_label.pack(anchor='w')

        # Panel s grafem
        self.plot_frame = ttk.LabelFrame(bottom_pane, text="Waveforma a hranice skladeb", padding=10)
        bottom_pane.add(self.plot_frame, weight=3)
        
        self.fig, self.ax = plt.subplots(facecolor='#f0f0f0')
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.plot_frame)
        self.canvas_widget = self.canvas.get_tk_widget()
        self.canvas_widget.pack(fill=tk.BOTH, expand=True)
        self.init_plot()
        
        # Status bar
        self.status = ttk.Label(root, text="Připraveno", style="Status.TLabel", anchor='w', relief=tk.SUNKEN, padding=5)
        self.status.pack(side=tk.BOTTOM, fill=tk.X)
        
        # --- Proměnné aplikace ---
        self.file_path = ""
        self.audio_data = None
        self.samplerate = None
        self.analysis_thread = None

    def _on_method_change(self, *args):
        """Callback pro změnu metody detekce - ovládá stav parametrů"""
        if self.use_onsets.get():
            # Onset metoda - deaktivovat parametry pro detekci ticha
            self.silence_scale.config(state=tk.DISABLED)
            for widget in self.silence_scale.master.winfo_children():
                if isinstance(widget, ttk.Scale) and widget != self.silence_scale:
                    widget.config(state=tk.DISABLED)
        else:
            # Metoda ticha - aktivovat všechny parametry
            self.silence_scale.config(state=tk.NORMAL)
            for widget in self.silence_scale.master.master.winfo_children():
                if isinstance(widget, ttk.Frame):
                    for child in widget.winfo_children():
                        if isinstance(child, ttk.Scale):
                            child.config(state=tk.NORMAL)

    def open_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("WAV soubory", "*.wav")])
        if not file_path: return
            
        self.file_path = file_path
        self.file_label.config(text=os.path.basename(file_path))
        self.btn_analyze.config(state=tk.NORMAL)
        self.clear_results()
        self.status.config(text="Načítám informace o souboru...")
        
        try:
            info = sf.info(self.file_path)
            self.samplerate = info.samplerate
            duration_sec = info.duration
            self.duration_label.config(text=f"Celková délka: {self.format_duration(duration_sec)}  |  Vzorkovací frekvence: {self.samplerate} Hz")
            self.status.config(text="Soubor připraven k analýze")
        except Exception as e:
            self.show_error(f"Nelze přečíst informace o souboru: {e}")
            self.file_path = ""

    def start_analysis(self):
        if not self.file_path: return
        self.set_ui_state(tk.DISABLED)
        self.update_progress(0)
        self.status.config(text="Spouštím analýzu...")
        self.clear_results()
        self.analysis_thread = threading.Thread(target=self.analyze_audio_vectorized, daemon=True)
        self.analysis_thread.start()

    def _detect_by_onsets(self, file_path):
        """Detekce skladeb pomocí onset analýzy (librosa)"""
        if not LIBROSA_OK:
            raise RuntimeError("Modul librosa není nainstalovaný.")

        self.update_status("Načítám audio přes librosa...")
        self.update_progress(10)
        y, sr = librosa.load(file_path, sr=None, mono=True)
        self.samplerate = sr
        # Pro vizualizaci potřebujeme stereo formát
        self.audio_data = y.reshape(-1, 1) if y.ndim == 1 else y

        self.update_status("Hledám onsety...")
        self.update_progress(50)
        onset_frames = librosa.onset.onset_detect(
            y=y, sr=sr, hop_length=512, backtrack=False)
        onset_times = librosa.frames_to_time(
            onset_frames, sr=sr, hop_length=512)
        self.update_progress(80)

        # Vytvoření hranic skladeb z onsetů
        duration_samples = len(y)
        min_track_len = self.min_track_var.get()

        # Začínáme od začátku souboru (0)
        boundaries = [0]

        # Přidáme všechny detekované onsety jako hranice
        for t in onset_times:
            boundary_sample = librosa.time_to_samples(t, sr=sr)
            boundaries.append(boundary_sample)

        # Přidáme konec souboru
        boundaries.append(duration_samples)
        boundaries = sorted(list(set(boundaries)))

        # Vytvoření seznamu skladeb
        track_boundaries_samples = []
        for i in range(len(boundaries) - 1):
            start_sample = boundaries[i]
            end_sample = boundaries[i + 1]
            duration_sec = (end_sample - start_sample) / sr
            if duration_sec > min_track_len:
                track_boundaries_samples.append((start_sample, end_sample))

        return track_boundaries_samples

    def _detect_by_silence(self, file_path):
        """Detekce skladeb pomocí analýzy tichých míst"""
        data, samplerate = sf.read(file_path, always_2d=True)
        self.audio_data = data
        self.samplerate = samplerate

        # Oprava pro mono soubory
        if data.ndim > 1:
            mono_data = np.mean(data, axis=1)
        else:
            mono_data = data

        self.update_status("Hledám tichá místa (vektorizovaná metoda)...")
        self.update_progress(25)

        # Optimalizace paměti - nepoužívat normalizaci
        max_val = np.max(np.abs(mono_data))
        silence_threshold = self.silence_threshold_var.get()
        absolute_threshold = silence_threshold * max_val if max_val > 0 else silence_threshold
        min_silence_duration = self.min_silence_var.get()
        min_silence_samples = int(samplerate * min_silence_duration)

        is_silent = np.abs(mono_data) < absolute_threshold
        self.update_progress(50)

        state_changes = np.diff(is_silent.astype(np.int8))
        silence_starts = np.where(state_changes == 1)[0] + 1
        silence_ends = np.where(state_changes == -1)[0] + 1
        self.update_progress(75)

        # Opravená logika detekce - hranice skladeb jsou konce tichých úseků
        boundaries = [0]  # Začínáme od začátku souboru

        if len(silence_starts) > 0 and len(silence_ends) > 0:
            # Zajistíme správné párování začátků a konců ticha
            if silence_ends[0] < silence_starts[0]:
                silence_starts = np.insert(silence_starts, 0, 0)
            if silence_starts[-1] > silence_ends[-1]:
                silence_ends = np.append(silence_ends, len(mono_data))

            # Přidáme konce dostatečně dlouhých tichých úseků jako hranice skladeb
            for start, end in zip(silence_starts, silence_ends):
                if (end - start) >= min_silence_samples:
                    boundaries.append(end)  # Konec ticha = hranice skladby

        boundaries.append(len(mono_data))  # Konec souboru
        boundaries = sorted(list(set(boundaries)))

        # Vytvoření seznamu skladeb
        track_boundaries_samples = []
        min_track_len = self.min_track_var.get()
        for i in range(len(boundaries) - 1):
            start_sample, end_sample = boundaries[i], boundaries[i+1]
            duration_sec = (end_sample - start_sample) / samplerate
            # Kontrola minimální délky a že segment není tichý
            if duration_sec > min_track_len:
                segment_is_silent = np.all(is_silent[start_sample:end_sample])
                if not segment_is_silent:
                    track_boundaries_samples.append((start_sample, end_sample))

        return track_boundaries_samples

    def analyze_audio_vectorized(self):
        """Hlavní metoda analýzy - volá příslušnou detekční metodu"""
        try:
            self.update_status("Načítám audio data do paměti...")
            start_time = time.time()

            if self.use_onsets.get():
                track_boundaries_samples = self._detect_by_onsets(self.file_path)
            else:
                track_boundaries_samples = self._detect_by_silence(self.file_path)

            total_time = time.time() - start_time
            self.update_status(f"Analýza dokončena: {len(track_boundaries_samples)} skladeb nalezeno za {total_time:.2f}s")
            self.root.after(0, self.show_results, track_boundaries_samples)

        except Exception as e:
            self.root.after(0, self.show_error, f"Během analýzy došlo k chybě: {e}")
        finally:
            self.root.after(0, self.set_ui_state, tk.NORMAL)

    def show_results(self, track_boundaries_samples):
        self.clear_results_list()

        if not track_boundaries_samples:
            ttk.Label(self.results_content_frame, text="Nebyla detekována žádná skladba.").pack(pady=10)
            self.summary_label.config(text="")
        else:
            total_detected_duration = 0
            for i, (start, end) in enumerate(track_boundaries_samples):
                start_time = start / self.samplerate
                end_time = end / self.samplerate
                duration = end_time - start_time
                total_detected_duration += duration

                frame = ttk.Frame(self.results_content_frame)
                frame.pack(fill=tk.X, pady=2, padx=5)
                ttk.Label(frame, text=f"{i+1}.", width=3).pack(side=tk.LEFT)
                ttk.Label(frame, text=self.format_duration(start_time), width=12, anchor=tk.W).pack(side=tk.LEFT)
                ttk.Label(frame, text=self.format_duration(end_time), width=12, anchor=tk.W).pack(side=tk.LEFT)
                ttk.Label(frame, text=self.format_duration(duration), width=12, anchor=tk.W).pack(side=tk.LEFT)

            # Souhrn výsledků
            if hasattr(self, 'audio_data') and self.audio_data is not None:
                if self.audio_data.ndim > 1:
                    total_file_duration = len(self.audio_data) / self.samplerate
                else:
                    total_file_duration = len(self.audio_data) / self.samplerate
                coverage_percent = (total_detected_duration / total_file_duration) * 100
                summary_text = f"Celkem: {len(track_boundaries_samples)} skladeb | Pokrytí: {self.format_duration(total_detected_duration)} / {self.format_duration(total_file_duration)} ({coverage_percent:.1f}%)"
                self.summary_label.config(text=summary_text)

        self.update_progress(100)
        self.plot_waveform(track_boundaries_samples)

    def plot_waveform(self, boundaries_samples):
        self.ax.clear()

        # Oprava pro mono soubory
        if self.audio_data.ndim > 1:
            mono_data = np.mean(self.audio_data, axis=1)
        else:
            mono_data = self.audio_data
        
        # Downsampling pro rychlé vykreslení (max 50k bodů)
        max_points = 50000
        step = max(1, len(mono_data) // max_points)
        plot_data = mono_data[::step]
        time_axis = np.linspace(0, len(mono_data) / self.samplerate, len(plot_data))
        
        self.ax.plot(time_axis, plot_data, color='#007acc', linewidth=0.7)
        
        # Vykreslení hranic skladeb
        track_starts = [s / self.samplerate for s, e in boundaries_samples]
        for start_time in track_starts[1:]:
            self.ax.axvline(x=start_time, color='r', linestyle='--', linewidth=1.2, label='Hranice skladby')

        if len(track_starts) > 1:
            handles, labels = self.ax.get_legend_handles_labels()
            by_label = dict(zip(labels, handles))
            self.ax.legend(by_label.values(), by_label.keys(), loc='upper right')

        self.ax.set_title("Audio Waveforma", fontsize=10)
        self.ax.set_xlabel("Čas", fontsize=9)
        self.ax.set_ylabel("Amplituda", fontsize=9)

        # Sjednocení časového formátu s tabulkou
        def time_formatter(x, pos):
            minutes = int(x // 60)
            seconds = x % 60
            return f"{minutes:02d}:{seconds:06.3f}"

        self.ax.xaxis.set_major_formatter(FuncFormatter(time_formatter))
        self.ax.grid(True, linestyle=':', alpha=0.6)
        self.ax.margins(x=0.01)
        self.fig.tight_layout()
        self.canvas.draw()

    def init_plot(self):
        self.ax.clear()
        self.ax.set_title("Načtěte soubor a spusťte analýzu", fontsize=10)
        self.ax.set_xlabel("Čas (s)", fontsize=9)
        self.ax.set_ylabel("Amplituda", fontsize=9)
        self.ax.grid(True, linestyle=':', alpha=0.6)
        self.fig.tight_layout()
        self.canvas.draw()

    def format_duration(self, seconds):
        minutes = int(seconds // 60)
        seconds_rem = seconds % 60
        return f"{minutes:02d}:{seconds_rem:06.3f}"

    def show_error(self, error_msg):
        messagebox.showerror("Chyba", error_msg)
        self.status.config(text="Chyba při analýze")

    def set_ui_state(self, state):
        self.btn_open.config(state=state)
        self.btn_analyze.config(state=state if self.file_path else tk.DISABLED)

    def clear_results_list(self):
        for widget in self.results_content_frame.winfo_children():
            widget.destroy()

    def clear_results(self):
        self.clear_results_list()
        self.init_plot()

    def update_progress(self, value):
        value = int(value)
        self.root.after(0, lambda: self.progress.config(value=value))
        self.root.after(0, lambda: self.progress_label.config(text=f"{value}%"))

    def update_status(self, message):
        self.root.after(0, lambda: self.status.config(text=message))

if __name__ == "__main__":
    root = tk.Tk()
    app = TrackDetectorApp(root)
    root.mainloop()