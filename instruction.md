🔧 1. Nové importy na začátek souboru
Hned za řádek
import matplotlib.pyplot as plt
přidej:
Python
Copy
try:
    import librosa
    LIBROSA_OK = True
except ImportError:
    LIBROSA_OK = False
🔧 2. Nový GUI prvek – zařadit za analysis_frame
Najdi sekci:
Python
Copy
        # Tlačítko analýzy a progress bar
        analysis_frame = ttk.Frame(top_frame)
        analysis_frame.pack(fill=tk.X, pady=5)
        ...
Přidej pod ní (stále v rámci top_frame):
Python
Copy
        # --- Výběr metody detekce ---
        method_frame = ttk.Frame(top_frame)
        method_frame.pack(fill=tk.X, pady=(5, 0))
        self.use_onsets = tk.BooleanVar(value=False)
        self.chk_onsets = ttk.Checkbutton(
            method_frame,
            text="Použít pomalou (onset) metodu pro kontinuální nahrávky",
            variable=self.use_onsets)
        self.chk_onsets.pack(anchor='w')
        if not LIBROSA_OK:
            self.chk_onsets.configure(state=tk.DISABLED)
🔧 3. Nová logika uvnitř analyze_audio_vectorized()
Nahraď celý blok, který začíná:
Python
Copy
        self.update_status("Načítám audio data do paměti...")
začátek nahraď:
Python
Copy
        self.update_status("Načítám audio data do paměti...")
        start_time = time.time()

        if self.use_onsets.get():
            if not LIBROSA_OK:
                raise RuntimeError("Modul librosa není nainstalovaný.")

            self.update_status("Načítám audio přes librosa...")
            y, sr = librosa.load(self.file_path, sr=None, mono=True)
            self.samplerate = sr

            self.update_status("Hledám onsety...")
            self.update_progress(25)
            onset_frames = librosa.onset.onset_detect(
                y=y, sr=sr, hop_length=512, backtrack=True)
            onset_times = librosa.frames_to_time(
                onset_frames, sr=sr, hop_length=512)
            self.update_progress(75)

            # Přidáme konec souboru jako poslední hranici
            track_boundaries_samples = []
            duration_samples = len(y)
            for i, t in enumerate(onset_times):
                start = librosa.time_to_samples(t, sr=sr)
                end = librosa.time_to_samples(
                    onset_times[i+1], sr=sr) if i+1 < len(onset_times) else duration_samples
                if (end - start) / sr > 0.5:
                    track_boundaries_samples.append((start, end))

        else:
            # původní rychlá metoda ticha
            data, samplerate = sf.read(self.file_path, always_2d=True)
            self.audio_data = data
            self.samplerate = samplerate

            mono_data = np.mean(data, axis=1)
            max_val = np.max(np.abs(mono_data))
            if max_val > 0:
                mono_data = mono_data / max_val

            self.update_status("Hledám tichá místa (vektorizovaná metoda)...")
            self.update_progress(25)

            silence_threshold = 0.02
            min_silence_duration = 1.0
            min_silence_samples = int(samplerate * min_silence_duration)

            is_silent = np.abs(mono_data) < silence_threshold
            self.update_progress(50)

            state_changes = np.diff(is_silent.astype(np.int8))
            silence_starts = np.where(state_changes == 1)[0] + 1
            silence_ends = np.where(state_changes == -1)[0] + 1
            self.update_progress(75)

            if len(silence_starts) == 0 or len(silence_ends) == 0:
                track_boundaries_samples = [(0, len(mono_data))]
            else:
                if silence_ends[0] < silence_starts[0]:
                    silence_starts = np.insert(silence_starts, 0, 0)
                if silence_starts[-1] > silence_ends[-1]:
                    silence_ends = np.append(silence_ends, len(mono_data))

                boundaries = [0]
                for start, end in zip(silence_starts, silence_ends):
                    if (end - start) >= min_silence_samples:
                        boundaries.append(start)
                        boundaries.append(end)
                boundaries.append(len(mono_data))
                boundaries = sorted(list(set(boundaries)))

                track_boundaries_samples = []
                for i in range(len(boundaries) - 1):
                    start_sample, end_sample = boundaries[i], boundaries[i+1]
                    if (end_sample - start_sample) / samplerate > 0.5 and not np.all(is_silent[start_sample:end_sample]):
                        track_boundaries_samples.append((start_sample, end_sample))

Zde je strohý seznam příkazů pro tvého AI kódovacího asistenta – nic navíc, jen to, co přidat do aktuálního kódu:

---

### 1. Import
```python
import librosa
```

---

### 2. GUI – slider pro prah ticha
```python
self.silence_threshold_var = tk.DoubleVar(value=0.02)
sil_frame = ttk.Frame(top_frame)
sil_frame.pack(fill=tk.X, pady=5)
ttk.Label(sil_frame, text="Prah ticha:").pack(side=tk.LEFT)
ttk.Scale(sil_frame, from_=0.005, to=0.1, orient=tk.HORIZONTAL,
          variable=self.silence_threshold_var, resolution=0.005).pack(fill=tk.X, expand=True)
```

---

### 3. Použití proměnné v rychlé metodě
```python
silence_threshold = self.silence_threshold_var.get()
```

---

### 4. Progress bar – onset metoda
```python
self.update_progress(10)   # load
y, sr = librosa.load(self.file_path, sr=None, mono=True)
self.update_progress(50)   # onset
onset_frames = librosa.onset.onset_detect(y=y, sr=sr, hop_length=512, backtrack=True)
self.update_progress(80)   # peak-pick
```

---

### 5. Minimální délka skladby
```python
min_track_len = 5.0   # sekundy
# filtr: if (end - start) / sr > min_track_len:
```